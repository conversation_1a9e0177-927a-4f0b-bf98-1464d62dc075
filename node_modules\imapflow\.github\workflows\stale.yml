name: 'Close stale issues and PRs'
on:
    schedule:
        - cron: '30 1 * * *'

jobs:
    stale:
        runs-on: ubuntu-latest
        steps:
            - uses: actions/stale@v8
              with:
                  stale-issue-message: 'This issue is stale because it has been open 30 days with no activity. Remove stale label or comment or this will be closed in 5 days.'
                  stale-pr-message: 'This PR is stale because it has been open 45 days with no activity. Remove stale label or comment or this will be closed in 10 days.'
                  close-issue-message: 'This issue was closed because it has been stalled for 15 days with no activity.'
                  close-pr-message: 'This PR was closed because it has been stalled for 20 days with no activity.'
                  days-before-issue-stale: 30
                  days-before-pr-stale: 45
                  days-before-issue-close: 15
                  days-before-pr-close: 20
                  stale-issue-label: 'no-issue-activity'
                  exempt-issue-labels: 'pending,work-in-progress'
                  stale-pr-label: 'no-pr-activity'
                  exempt-pr-labels: 'awaiting-approval,work-in-progress'
                  repo-token: ${{ secrets.GITHUB_TOKEN }}
