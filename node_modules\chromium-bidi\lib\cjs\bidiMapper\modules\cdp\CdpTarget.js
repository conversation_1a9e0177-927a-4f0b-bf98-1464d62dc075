"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CdpTarget = void 0;
const chromium_bidi_js_1 = require("../../../protocol/chromium-bidi.js");
const protocol_js_1 = require("../../../protocol/protocol.js");
const Deferred_js_1 = require("../../../utils/Deferred.js");
const log_js_1 = require("../../../utils/log.js");
const BrowsingContextImpl_js_1 = require("../context/BrowsingContextImpl.js");
const LogManager_js_1 = require("../log/LogManager.js");
class CdpTarget {
    #id;
    #userContext;
    #cdpClient;
    #browserCdpClient;
    #parentCdpClient;
    #realmStorage;
    #eventManager;
    #preloadScriptStorage;
    #browsingContextStorage;
    #networkStorage;
    contextConfigStorage;
    #unblocked = new Deferred_js_1.Deferred();
    #logger;
    // Keeps track of the previously set viewport.
    #previousDeviceMetricsOverride = {
        width: 0,
        height: 0,
        deviceScaleFactor: 0,
        mobile: false,
        dontSetVisibleSize: true,
    };
    /**
     * Target's window id. Is filled when the CDP target is created and do not reflect
     * moving targets from one window to another. The actual values
     * will be set during `#unblock`.
     * */
    #windowId;
    #deviceAccessEnabled = false;
    #cacheDisableState = false;
    #fetchDomainStages = {
        request: false,
        response: false,
        auth: false,
    };
    static create(targetId, cdpClient, browserCdpClient, parentCdpClient, realmStorage, eventManager, preloadScriptStorage, browsingContextStorage, networkStorage, configStorage, userContext, logger) {
        const cdpTarget = new CdpTarget(targetId, cdpClient, browserCdpClient, parentCdpClient, eventManager, realmStorage, preloadScriptStorage, browsingContextStorage, configStorage, networkStorage, userContext, logger);
        LogManager_js_1.LogManager.create(cdpTarget, realmStorage, eventManager, logger);
        cdpTarget.#setEventListeners();
        // No need to await.
        // Deferred will be resolved when the target is unblocked.
        void cdpTarget.#unblock();
        return cdpTarget;
    }
    constructor(targetId, cdpClient, browserCdpClient, parentCdpClient, eventManager, realmStorage, preloadScriptStorage, browsingContextStorage, configStorage, networkStorage, userContext, logger) {
        this.#userContext = userContext;
        this.#id = targetId;
        this.#cdpClient = cdpClient;
        this.#browserCdpClient = browserCdpClient;
        this.#parentCdpClient = parentCdpClient;
        this.#eventManager = eventManager;
        this.#realmStorage = realmStorage;
        this.#preloadScriptStorage = preloadScriptStorage;
        this.#networkStorage = networkStorage;
        this.#browsingContextStorage = browsingContextStorage;
        this.contextConfigStorage = configStorage;
        this.#logger = logger;
    }
    /** Returns a deferred that resolves when the target is unblocked. */
    get unblocked() {
        return this.#unblocked;
    }
    get id() {
        return this.#id;
    }
    get cdpClient() {
        return this.#cdpClient;
    }
    get parentCdpClient() {
        return this.#parentCdpClient;
    }
    get browserCdpClient() {
        return this.#browserCdpClient;
    }
    /** Needed for CDP escape path. */
    get cdpSessionId() {
        // SAFETY we got the client by it's id for creating
        return this.#cdpClient.sessionId;
    }
    /**
     * Window id the target belongs to. If not known, returns 0.
     */
    get windowId() {
        if (this.#windowId === undefined) {
            this.#logger?.(log_js_1.LogType.debugError, 'Getting windowId before it was set, returning 0');
        }
        return this.#windowId ?? 0;
    }
    /**
     * Enables all the required CDP domains and unblocks the target.
     */
    async #unblock() {
        try {
            await Promise.all([
                this.#cdpClient.sendCommand('Page.enable', {
                    enableFileChooserOpenedEvent: true,
                }),
                ...(this.#ignoreFileDialog()
                    ? []
                    : [
                        this.#cdpClient.sendCommand('Page.setInterceptFileChooserDialog', {
                            enabled: true,
                            // The intercepted dialog should be canceled.
                            cancel: true,
                        }),
                    ]),
                // There can be some existing frames in the target, if reconnecting to an
                // existing browser instance, e.g. via Puppeteer. Need to restore the browsing
                // contexts for the frames to correctly handle further events, like
                // `Runtime.executionContextCreated`.
                // It's important to schedule this task together with enabling domains commands to
                // prepare the tree before the events (e.g. Runtime.executionContextCreated) start
                // coming.
                // https://github.com/GoogleChromeLabs/chromium-bidi/issues/2282
                this.#cdpClient
                    .sendCommand('Page.getFrameTree')
                    .then((frameTree) => this.#restoreFrameTreeState(frameTree.frameTree)),
                this.#cdpClient.sendCommand('Runtime.enable'),
                this.#cdpClient.sendCommand('Page.setLifecycleEventsEnabled', {
                    enabled: true,
                }),
                // Enabling CDP Network domain is required for navigation detection:
                // https://github.com/GoogleChromeLabs/chromium-bidi/issues/2856.
                this.#cdpClient
                    .sendCommand('Network.enable')
                    .then(() => this.toggleNetworkIfNeeded()),
                this.#cdpClient.sendCommand('Target.setAutoAttach', {
                    autoAttach: true,
                    waitForDebuggerOnStart: true,
                    flatten: true,
                }),
                this.#updateWindowId(),
                this.#setUserContextConfig(),
                this.#initAndEvaluatePreloadScripts(),
                this.#cdpClient.sendCommand('Runtime.runIfWaitingForDebugger'),
                // Resume tab execution as well if it was paused by the debugger.
                this.#parentCdpClient.sendCommand('Runtime.runIfWaitingForDebugger'),
                this.toggleDeviceAccessIfNeeded(),
            ]);
        }
        catch (error) {
            this.#logger?.(log_js_1.LogType.debugError, 'Failed to unblock target', error);
            // The target might have been closed before the initialization finished.
            if (!this.#cdpClient.isCloseError(error)) {
                this.#unblocked.resolve({
                    kind: 'error',
                    error,
                });
                return;
            }
        }
        this.#unblocked.resolve({
            kind: 'success',
            value: undefined,
        });
    }
    #restoreFrameTreeState(frameTree) {
        const frame = frameTree.frame;
        const maybeContext = this.#browsingContextStorage.findContext(frame.id);
        if (maybeContext !== undefined) {
            // Restoring parent of already known browsing context. This means the target is
            // OOPiF and the BiDi session was connected to already existing browser instance.
            if (maybeContext.parentId === null &&
                frame.parentId !== null &&
                frame.parentId !== undefined) {
                maybeContext.parentId = frame.parentId;
            }
        }
        if (maybeContext === undefined && frame.parentId !== undefined) {
            // Restore not yet known nested frames. The top-level frame is created when the
            // target is attached.
            const parentBrowsingContext = this.#browsingContextStorage.getContext(frame.parentId);
            BrowsingContextImpl_js_1.BrowsingContextImpl.create(frame.id, frame.parentId, this.#userContext, parentBrowsingContext.cdpTarget, this.#eventManager, this.#browsingContextStorage, this.#realmStorage, this.contextConfigStorage, frame.url, undefined, this.#logger);
        }
        frameTree.childFrames?.map((frameTree) => this.#restoreFrameTreeState(frameTree));
    }
    async toggleFetchIfNeeded() {
        const stages = this.#networkStorage.getInterceptionStages(this.topLevelId);
        if (this.#fetchDomainStages.request === stages.request &&
            this.#fetchDomainStages.response === stages.response &&
            this.#fetchDomainStages.auth === stages.auth) {
            return;
        }
        const patterns = [];
        this.#fetchDomainStages = stages;
        if (stages.request || stages.auth) {
            // CDP quirk we need request interception when we intercept auth
            patterns.push({
                urlPattern: '*',
                requestStage: 'Request',
            });
        }
        if (stages.response) {
            patterns.push({
                urlPattern: '*',
                requestStage: 'Response',
            });
        }
        if (patterns.length) {
            await this.#cdpClient.sendCommand('Fetch.enable', {
                patterns,
                handleAuthRequests: stages.auth,
            });
        }
        else {
            const blockedRequest = this.#networkStorage
                .getRequestsByTarget(this)
                .filter((request) => request.interceptPhase);
            void Promise.allSettled(blockedRequest.map((request) => request.waitNextPhase))
                .then(async () => {
                const blockedRequest = this.#networkStorage
                    .getRequestsByTarget(this)
                    .filter((request) => request.interceptPhase);
                if (blockedRequest.length) {
                    return await this.toggleFetchIfNeeded();
                }
                return await this.#cdpClient.sendCommand('Fetch.disable');
            })
                .catch((error) => {
                this.#logger?.(log_js_1.LogType.bidi, 'Disable failed', error);
            });
        }
    }
    /**
     * Toggles CDP "Fetch" domain and enable/disable network cache.
     */
    async toggleNetworkIfNeeded() {
        // Although the Network domain remains active, Fetch domain activation and caching
        // settings should be managed dynamically.
        try {
            await Promise.all([
                this.toggleSetCacheDisabled(),
                this.toggleFetchIfNeeded(),
            ]);
        }
        catch (err) {
            this.#logger?.(log_js_1.LogType.debugError, err);
            if (!this.#isExpectedError(err)) {
                throw err;
            }
        }
    }
    async toggleSetCacheDisabled(disable) {
        const defaultCacheDisabled = this.#networkStorage.defaultCacheBehavior === 'bypass';
        const cacheDisabled = disable ?? defaultCacheDisabled;
        if (this.#cacheDisableState === cacheDisabled) {
            return;
        }
        this.#cacheDisableState = cacheDisabled;
        try {
            await this.#cdpClient.sendCommand('Network.setCacheDisabled', {
                cacheDisabled,
            });
        }
        catch (err) {
            this.#logger?.(log_js_1.LogType.debugError, err);
            this.#cacheDisableState = !cacheDisabled;
            if (!this.#isExpectedError(err)) {
                throw err;
            }
        }
    }
    async toggleDeviceAccessIfNeeded() {
        const enabled = this.isSubscribedTo(chromium_bidi_js_1.Bluetooth.EventNames.RequestDevicePromptUpdated);
        if (this.#deviceAccessEnabled === enabled) {
            return;
        }
        this.#deviceAccessEnabled = enabled;
        try {
            await this.#cdpClient.sendCommand(enabled ? 'DeviceAccess.enable' : 'DeviceAccess.disable');
        }
        catch (err) {
            this.#logger?.(log_js_1.LogType.debugError, err);
            this.#deviceAccessEnabled = !enabled;
            if (!this.#isExpectedError(err)) {
                throw err;
            }
        }
    }
    /**
     * Heuristic checking if the error is due to the session being closed. If so, ignore the
     * error.
     */
    #isExpectedError(err) {
        const error = err;
        return ((error.code === -32001 &&
            error.message === 'Session with given id not found.') ||
            this.#cdpClient.isCloseError(err));
    }
    #setEventListeners() {
        this.#cdpClient.on('*', (event, params) => {
            // We may encounter uses for EventEmitter other than CDP events,
            // which we want to skip.
            if (typeof event !== 'string') {
                return;
            }
            this.#eventManager.registerEvent({
                type: 'event',
                method: `goog:cdp.${event}`,
                params: {
                    event,
                    params,
                    session: this.cdpSessionId,
                },
            }, this.id);
        });
    }
    async #enableFetch(stages) {
        const patterns = [];
        if (stages.request || stages.auth) {
            // CDP quirk we need request interception when we intercept auth
            patterns.push({
                urlPattern: '*',
                requestStage: 'Request',
            });
        }
        if (stages.response) {
            patterns.push({
                urlPattern: '*',
                requestStage: 'Response',
            });
        }
        if (patterns.length) {
            const oldStages = this.#fetchDomainStages;
            this.#fetchDomainStages = stages;
            try {
                await this.#cdpClient.sendCommand('Fetch.enable', {
                    patterns,
                    handleAuthRequests: stages.auth,
                });
            }
            catch {
                this.#fetchDomainStages = oldStages;
            }
        }
    }
    async #disableFetch() {
        const blockedRequest = this.#networkStorage
            .getRequestsByTarget(this)
            .filter((request) => request.interceptPhase);
        if (blockedRequest.length === 0) {
            this.#fetchDomainStages = {
                request: false,
                response: false,
                auth: false,
            };
            await this.#cdpClient.sendCommand('Fetch.disable');
        }
    }
    async toggleNetwork() {
        // TODO: respect the data collectors once CDP Network domain is enabled on-demand:
        // const networkEnable = this.#networkStorage.getCollectorsForBrowsingContext(this.topLevelId).length > 0;
        const stages = this.#networkStorage.getInterceptionStages(this.topLevelId);
        const fetchEnable = Object.values(stages).some((value) => value);
        const fetchChanged = this.#fetchDomainStages.request !== stages.request ||
            this.#fetchDomainStages.response !== stages.response ||
            this.#fetchDomainStages.auth !== stages.auth;
        this.#logger?.(log_js_1.LogType.debugInfo, 'Toggle Network', `Fetch (${fetchEnable}) ${fetchChanged}`);
        if (fetchEnable && fetchChanged) {
            await this.#enableFetch(stages);
        }
        if (!fetchEnable && fetchChanged) {
            await this.#disableFetch();
        }
    }
    /**
     * All the ProxyChannels from all the preload scripts of the given
     * BrowsingContext.
     */
    getChannels() {
        return this.#preloadScriptStorage
            .find()
            .flatMap((script) => script.channels);
    }
    async #updateWindowId() {
        const { windowId } = await this.#browserCdpClient.sendCommand('Browser.getWindowForTarget', { targetId: this.id });
        this.#windowId = windowId;
    }
    /** Loads all top-level preload scripts. */
    async #initAndEvaluatePreloadScripts() {
        await Promise.all(this.#preloadScriptStorage
            .find({
            // Needed for OOPIF
            targetId: this.topLevelId,
        })
            .map((script) => {
            return script.initInTarget(this, true);
        }));
    }
    async setViewport(viewport, devicePixelRatio) {
        if (viewport === null && devicePixelRatio === null) {
            await this.cdpClient.sendCommand('Emulation.clearDeviceMetricsOverride');
            return;
        }
        const newViewport = { ...this.#previousDeviceMetricsOverride };
        if (viewport === null) {
            // Disable override.
            newViewport.width = 0;
            newViewport.height = 0;
        }
        else if (viewport !== undefined) {
            newViewport.width = viewport.width;
            newViewport.height = viewport.height;
        }
        if (devicePixelRatio === null) {
            // Disable override.
            newViewport.deviceScaleFactor = 0;
        }
        else if (devicePixelRatio !== undefined) {
            newViewport.deviceScaleFactor = devicePixelRatio;
        }
        try {
            await this.cdpClient.sendCommand('Emulation.setDeviceMetricsOverride', newViewport);
            this.#previousDeviceMetricsOverride = newViewport;
        }
        catch (err) {
            if (err.message.startsWith(
            // https://crsrc.org/c/content/browser/devtools/protocol/emulation_handler.cc;l=257;drc=2f6eee84cf98d4227e7c41718dd71b82f26d90ff
            'Width and height values must be positive')) {
                throw new protocol_js_1.UnsupportedOperationException('Provided viewport dimensions are not supported');
            }
            throw err;
        }
    }
    /**
     * Immediately schedules all the required commands to configure user context
     * configuration and waits for them to finish. It's important to schedule them
     * in parallel, so that they are enqueued before any page's scripts.
     */
    async #setUserContextConfig() {
        const promises = [];
        const config = this.contextConfigStorage.getActiveConfig(this.topLevelId, this.#userContext);
        promises.push(this.#cdpClient
            .sendCommand('Page.setPrerenderingAllowed', {
            isAllowed: !config.prerenderingDisabled,
        })
            .catch(() => {
            // Ignore CDP errors, as the command is not supported by iframe targets or
            // prerendered pages. Generic catch, as the error can vary between CdpClient
            // implementations: Tab vs Puppeteer.
        }));
        if (config.viewport !== undefined ||
            config.devicePixelRatio !== undefined) {
            promises.push(this.setViewport(config.viewport, config.devicePixelRatio).catch(() => {
                // Ignore CDP errors, as the command is not supported by iframe targets. Generic
                // catch, as the error can vary between CdpClient implementations: Tab vs
                // Puppeteer.
            }));
        }
        if (config.screenOrientation !== undefined &&
            config.screenOrientation !== null) {
            promises.push(this.setScreenOrientationOverride(config.screenOrientation).catch(() => {
                // Ignore CDP errors, as the command is not supported by iframe targets.
                // Generic catch, as the error can vary between CdpClient implementations:
                // Tab vs Puppeteer.
            }));
        }
        if (config.geolocation !== undefined && config.geolocation !== null) {
            promises.push(this.setGeolocationOverride(config.geolocation));
        }
        if (config.locale !== undefined) {
            promises.push(this.setLocaleOverride(config.locale));
        }
        if (config.timezone !== undefined) {
            promises.push(this.setTimezoneOverride(config.timezone));
        }
        if (config.extraHeaders !== undefined) {
            promises.push(this.setExtraHeaders(config.extraHeaders));
        }
        if (config.acceptInsecureCerts !== undefined) {
            promises.push(this.cdpClient.sendCommand('Security.setIgnoreCertificateErrors', {
                ignore: config.acceptInsecureCerts,
            }));
        }
        await Promise.all(promises);
    }
    get topLevelId() {
        return (this.#browsingContextStorage.findTopLevelContextId(this.id) ?? this.id);
    }
    isSubscribedTo(moduleOrEvent) {
        return this.#eventManager.subscriptionManager.isSubscribedTo(moduleOrEvent, this.topLevelId);
    }
    #ignoreFileDialog() {
        const config = this.contextConfigStorage.getActiveConfig(this.topLevelId, this.#userContext);
        return ((config.userPromptHandler?.file ??
            config.userPromptHandler?.default ??
            "ignore" /* Session.UserPromptHandlerType.Ignore */) ===
            "ignore" /* Session.UserPromptHandlerType.Ignore */);
    }
    async setGeolocationOverride(geolocation) {
        if (geolocation === null) {
            await this.cdpClient.sendCommand('Emulation.clearGeolocationOverride');
        }
        else if ('type' in geolocation) {
            if (geolocation.type !== 'positionUnavailable') {
                // Unreachable. Handled by params parser.
                throw new protocol_js_1.UnknownErrorException(`Unknown geolocation error ${geolocation.type}`);
            }
            // Omitting latitude, longitude or accuracy emulates position unavailable.
            await this.cdpClient.sendCommand('Emulation.setGeolocationOverride', {});
        }
        else if ('latitude' in geolocation) {
            await this.cdpClient.sendCommand('Emulation.setGeolocationOverride', {
                latitude: geolocation.latitude,
                longitude: geolocation.longitude,
                accuracy: geolocation.accuracy ?? 1,
                // `null` value is treated as "missing".
                altitude: geolocation.altitude ?? undefined,
                altitudeAccuracy: geolocation.altitudeAccuracy ?? undefined,
                heading: geolocation.heading ?? undefined,
                speed: geolocation.speed ?? undefined,
            });
        }
        else {
            // Unreachable. Handled by params parser.
            throw new protocol_js_1.UnknownErrorException('Unexpected geolocation coordinates value');
        }
    }
    async setScreenOrientationOverride(screenOrientation) {
        const newViewport = { ...this.#previousDeviceMetricsOverride };
        if (screenOrientation === null) {
            delete newViewport.screenOrientation;
        }
        else {
            newViewport.screenOrientation =
                this.#toCdpScreenOrientationAngle(screenOrientation);
        }
        await this.cdpClient.sendCommand('Emulation.setDeviceMetricsOverride', newViewport);
        this.#previousDeviceMetricsOverride = newViewport;
    }
    #toCdpScreenOrientationAngle(orientation) {
        // https://w3c.github.io/screen-orientation/#the-current-screen-orientation-type-and-angle
        if (orientation.natural === "portrait" /* Emulation.ScreenOrientationNatural.Portrait */) {
            switch (orientation.type) {
                case 'portrait-primary':
                    return {
                        angle: 0,
                        type: 'portraitPrimary',
                    };
                case 'landscape-primary':
                    return {
                        angle: 90,
                        type: 'landscapePrimary',
                    };
                case 'portrait-secondary':
                    return {
                        angle: 180,
                        type: 'portraitSecondary',
                    };
                case 'landscape-secondary':
                    return {
                        angle: 270,
                        type: 'landscapeSecondary',
                    };
                default:
                    // Unreachable.
                    throw new protocol_js_1.UnknownErrorException(`Unexpected screen orientation type ${orientation.type}`);
            }
        }
        if (orientation.natural === "landscape" /* Emulation.ScreenOrientationNatural.Landscape */) {
            switch (orientation.type) {
                case 'landscape-primary':
                    return {
                        angle: 0,
                        type: 'landscapePrimary',
                    };
                case 'portrait-primary':
                    return {
                        angle: 90,
                        type: 'portraitPrimary',
                    };
                case 'landscape-secondary':
                    return {
                        angle: 180,
                        type: 'landscapeSecondary',
                    };
                case 'portrait-secondary':
                    return {
                        angle: 270,
                        type: 'portraitSecondary',
                    };
                default:
                    // Unreachable.
                    throw new protocol_js_1.UnknownErrorException(`Unexpected screen orientation type ${orientation.type}`);
            }
        }
        // Unreachable.
        throw new protocol_js_1.UnknownErrorException(`Unexpected orientation natural ${orientation.natural}`);
    }
    async setLocaleOverride(locale) {
        if (locale === null) {
            await this.cdpClient.sendCommand('Emulation.setLocaleOverride', {});
        }
        else {
            await this.cdpClient.sendCommand('Emulation.setLocaleOverride', {
                locale,
            });
        }
    }
    async setTimezoneOverride(timezone) {
        if (timezone === null) {
            await this.cdpClient.sendCommand('Emulation.setTimezoneOverride', {
                // If empty, disables the override and restores default host system timezone.
                timezoneId: '',
            });
        }
        else {
            await this.cdpClient.sendCommand('Emulation.setTimezoneOverride', {
                timezoneId: timezone,
            });
        }
    }
    async setExtraHeaders(headers) {
        await this.cdpClient.sendCommand('Network.setExtraHTTPHeaders', {
            headers,
        });
    }
}
exports.CdpTarget = CdpTarget;
//# sourceMappingURL=CdpTarget.js.map