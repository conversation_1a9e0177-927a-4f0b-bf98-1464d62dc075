name: Run tests

on:
    push:
    pull_request:

jobs:
    test:
        strategy:
            matrix:
                node: [16.x, 18.x]
                os: [ubuntu-latest]
        runs-on: ${{ matrix.os }}
        steps:
            - uses: actions/checkout@v2
            - name: Use Node.js ${{ matrix.node }}
              uses: actions/setup-node@v1
              with:
                  node-version: ${{ matrix.node }}
            - run: npm install
            - run: npm test
