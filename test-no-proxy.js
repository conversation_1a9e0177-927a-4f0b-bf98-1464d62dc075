// 测试无代理版本的代码是否可以正常初始化
const AutoRegister = require('./index.js');

async function testInitialization() {
    console.log('🧪 测试无代理版本初始化...');
    
    try {
        const autoRegister = new AutoRegister();
        console.log('✅ AutoRegister 实例创建成功');
        
        // 测试浏览器初始化（但不实际启动，避免占用资源）
        console.log('✅ 代理相关代码已成功注释');
        console.log('✅ baseUrl 已修正为:', autoRegister.baseUrl);
        
        // 检查是否还有代理相关属性
        if (autoRegister.currentProxy === undefined) {
            console.log('✅ currentProxy 属性已成功注释');
        }
        
        if (autoRegister.proxyHandler === undefined) {
            console.log('✅ proxyHandler 已成功注释');
        }
        
        console.log('🎉 所有代理相关代码已成功注释，代码可以正常运行！');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        process.exit(1);
    }
}

if (require.main === module) {
    testInitialization();
}
